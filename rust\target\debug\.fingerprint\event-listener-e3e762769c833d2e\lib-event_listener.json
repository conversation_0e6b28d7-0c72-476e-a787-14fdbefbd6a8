{"rustc": 16591470773350601817, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 13827760451848848284, "path": 11073763239399110167, "deps": [[189982446159473706, "parking", false, 13365300189628372960], [1906322745568073236, "pin_project_lite", false, 17679645320038390663], [12100481297174703255, "concurrent_queue", false, 6883475370011111168]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\event-listener-e3e762769c833d2e\\dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}