{"rustc": 16591470773350601817, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 2241668132362809309, "path": 1890309593882786594, "deps": [[1288403060204016458, "tokio_util", false, 12073112882136515913], [1906322745568073236, "pin_project_lite", false, 17679645320038390663], [3129130049864710036, "memchr", false, 13088982977417235102], [5138218615291878843, "tokio_dep", false, 17812547867544043184], [7620660491849607393, "futures_core_03", false, 18155078109343662124], [16066129441945555748, "bytes", false, 6019563794509005783]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-16cd1a5285060635\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}