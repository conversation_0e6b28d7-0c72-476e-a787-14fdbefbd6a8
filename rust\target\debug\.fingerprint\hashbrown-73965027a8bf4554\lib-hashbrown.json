{"rustc": 16591470773350601817, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 4261756912893462379, "deps": [[9150530836556604396, "allocator_api2", false, 12823902803264382171], [10791833957791020630, "ahash", false, 1085498476776149442]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-73965027a8bf4554\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}