{"rustc": 16591470773350601817, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 16142783222888525446, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-a6a470ac9e480b73\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}