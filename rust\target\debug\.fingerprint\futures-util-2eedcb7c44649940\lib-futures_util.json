{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 575379996404361960, "deps": [[5103565458935487, "futures_io", false, 1356216582758342137], [1615478164327904835, "pin_utils", false, 6241250428614362800], [1811549171721445101, "futures_channel", false, 6605146123229134443], [1906322745568073236, "pin_project_lite", false, 17679645320038390663], [3129130049864710036, "memchr", false, 13088982977417235102], [6955678925937229351, "slab", false, 15754906758434063444], [7013762810557009322, "futures_sink", false, 2664794042779852552], [7620660491849607393, "futures_core", false, 18155078109343662124], [10565019901765856648, "futures_macro", false, 2634805938220637126], [16240732885093539806, "futures_task", false, 1103033267800850480]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-2eedcb7c44649940\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}