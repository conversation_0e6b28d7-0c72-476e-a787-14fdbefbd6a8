{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 7588797288915553443, "path": 4074056406410334228, "deps": [[14883207739598929556, "clap_builder", false, 9186062311853618738]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-8e7545cf556576b8\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}