{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 16061649671071484401, "deps": [[5103565458935487, "futures_io", false, 1356216582758342137], [1811549171721445101, "futures_channel", false, 6605146123229134443], [7013762810557009322, "futures_sink", false, 2664794042779852552], [7620660491849607393, "futures_core", false, 18155078109343662124], [10629569228670356391, "futures_util", false, 10047275903944279162], [12779779637805422465, "futures_executor", false, 6325330856200788267], [16240732885093539806, "futures_task", false, 1103033267800850480]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-c7cde41e311ac74f\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}