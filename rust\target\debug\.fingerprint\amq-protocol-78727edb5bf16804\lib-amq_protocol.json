{"rustc": 16591470773350601817, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 12659125817264579830, "profile": 2241668132362809309, "path": 17470530622782470673, "deps": [[4886105269790530060, "cookie_factory", false, 5749381305604230115], [6502365400774175331, "nom", false, 8865485522001809295], [6569408073262695432, "amq_protocol_tcp", false, 17053356345830777456], [7439207542593629805, "amq_protocol_types", false, 1429546829047830022], [7707838350479936199, "build_script_build", false, 14671304060818560606], [9689903380558560274, "serde", false, 12003910592217590912], [12003733154382565644, "amq_protocol_uri", false, 10392293566787602206]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-78727edb5bf16804\\dep-lib-amq_protocol", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}