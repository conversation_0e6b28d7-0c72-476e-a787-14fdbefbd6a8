{"rustc": 16591470773350601817, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17255432589167795725, "path": 11461701038127843174, "deps": [[1213098572879462490, "json5_rs", false, 17434763110541936656], [1965680986145237447, "yaml_rust2", false, 12452470319698361398], [2244620803250265856, "ron", false, 15142611786245242485], [6502365400774175331, "nom", false, 8865485522001809295], [6517602928339163454, "path<PERSON><PERSON>", false, 7229102392823451668], [8786711029710048183, "toml", false, 15071600365936907564], [9689903380558560274, "serde", false, 12003910592217590912], [11946729385090170470, "async_trait", false, 2851097757784993219], [13475460906694513802, "convert_case", false, 14181657675735942708], [14618892375165583068, "ini", false, 1808850129485626935], [15367738274754116744, "serde_json", false, 1445124509534137785]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-a2f729dec7728e84\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}