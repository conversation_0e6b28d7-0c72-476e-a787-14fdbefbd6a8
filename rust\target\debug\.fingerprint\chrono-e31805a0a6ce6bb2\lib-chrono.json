{"rustc": 16591470773350601817, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 2241668132362809309, "path": 8560305044025566907, "deps": [[1885655767270534569, "windows_link", false, 14550500534402263523], [5157631553186200874, "num_traits", false, 459184319286410902], [9689903380558560274, "serde", false, 12003910592217590912]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-e31805a0a6ce6bb2\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}