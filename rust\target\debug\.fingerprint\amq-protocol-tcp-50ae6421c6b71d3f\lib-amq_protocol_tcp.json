{"rustc": 16591470773350601817, "features": "[\"rustls-connector\", \"rustls-native-certs\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 11924301528678068555, "profile": 2241668132362809309, "path": 16209741678309577627, "deps": [[8606274917505247608, "tracing", false, 1128677179110668081], [12003733154382565644, "amq_protocol_uri", false, 10392293566787602206], [17059544261156971941, "tcp_stream", false, 15047122206065736681]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-tcp-50ae6421c6b71d3f\\dep-lib-amq_protocol_tcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}