name: Publish to Py<PERSON>

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      test_pypi:
        description: 'Publish to Test PyPI instead of PyPI'
        required: false
        default: false
        type: boolean

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.12"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: python -m build

    - name: Check package
      run: twine check dist/*

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  test-install:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12", "3.13"]
    steps:
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Test installation
      run: |
        pip install dist/*.whl
        python -c "import hybridpipe; print(f'HybridPipe {hybridpipe.__version__} installed successfully')"
        python -c "from hybridpipe import deploy_router, BrokerType; print('Core imports working')"

  publish:
    needs: [build, test-install]
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.event.inputs.test_pypi == 'true' && 'test-pypi' || 'pypi' }}
      url: ${{ github.event.inputs.test_pypi == 'true' && 'https://test.pypi.org/p/hybridpipe' || 'https://pypi.org/p/hybridpipe' }}
    permissions:
      id-token: write  # For trusted publishing

    steps:
    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Publish to Test PyPI
      if: github.event.inputs.test_pypi == 'true'
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/

    - name: Publish to PyPI
      if: github.event.inputs.test_pypi != 'true'
      uses: pypa/gh-action-pypi-publish@release/v1
